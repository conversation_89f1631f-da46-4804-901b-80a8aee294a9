#!/usr/bin/env python3
"""
Enhanced <PERSON><PERSON><PERSON> to copy files based on paths in a CSV file to an Output folder.

This script:
1. Reads a CSV file with a "True Path" column
2. Creates folder structure based on paths in the CSV
3. Copies files from source directory to Output directory maintaining original structure
4. Generates a comprehensive CSV log with detailed processing information
5. Provides statistics and summary information

Features:
- Supports command line arguments for source directory and CSV file
- Creates detailed processing logs similar to File_Processing_Log format
- Handles missing files gracefully with detailed error reporting
- Maintains original folder structure in output directory
- Provides comprehensive statistics and progress reporting
"""

import os
import csv
import shutil
import argparse
import datetime
import sys
import hashlib
import time
import gc
from pathlib import Path
from typing import List, Dict, Tuple, Optional

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Enhanced script to copy files listed in a CSV to an Output folder with original structure",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Copy files from source directory based on CSV file paths
  python copy_files_from_csv.py -s F:\\Project_Stargazing\\Encase\\Existing_actual_files -c Project_stargazing_encase_final_filtered.csv

  # Specify custom output directory
  python copy_files_from_csv.py -s F:\\Project_Stargazing\\Encase\\Existing_actual_files -c file_list.csv -o CustomOutput

  # Enable verbose output
  python copy_files_from_csv.py -s F:\\Project_Stargazing\\Encase\\Existing_actual_files -c file_list.csv -v
        """
    )

    parser.add_argument('-s', '--source', required=False,
                       help='Source directory containing the files to copy')
    parser.add_argument('-c', '--csv', required=False,
                       help='Path to the CSV file with "True Path" column')
    parser.add_argument('-o', '--output',
                       help='Output directory (default: "Output" in current directory)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose output for detailed progress information')
    parser.add_argument('--troubleshoot', action='store_true',
                       help='Show troubleshooting information for file lock issues')

    return parser.parse_args()

def get_file_info(file_path: str) -> Dict:
    """
    Get detailed information about a file.

    Args:
        file_path: Path to the file

    Returns:
        Dictionary containing file information
    """
    info = {
        "exists": False,
        "size": 0,
        "size_formatted": "0 B",
        "modified_time": "",
        "created_time": "",
        "is_directory": False,
        "extension": "",
        "md5_hash": ""
    }

    try:
        if os.path.exists(file_path):
            info["exists"] = True
            stat = os.stat(file_path)
            info["size"] = stat.st_size
            info["size_formatted"] = format_file_size(stat.st_size)
            info["modified_time"] = datetime.datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            info["created_time"] = datetime.datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S")
            info["is_directory"] = os.path.isdir(file_path)
            info["extension"] = os.path.splitext(file_path)[1].lower()

            # Calculate MD5 hash for files (not directories)
            if not info["is_directory"] and info["size"] < 100 * 1024 * 1024:  # Only for files < 100MB
                try:
                    with open(file_path, 'rb') as f:
                        info["md5_hash"] = hashlib.md5(f.read()).hexdigest()
                except:
                    info["md5_hash"] = "Error calculating hash"
    except Exception as e:
        info["error"] = str(e)

    return info

def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format.

    Args:
        size_bytes: Size in bytes

    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename to ensure it's valid for Windows while preserving special characters.

    Args:
        filename: Original filename

    Returns:
        Sanitized filename safe for Windows
    """
    # Windows forbidden characters: < > : " | ? * and control characters (0-31)
    forbidden_chars = '<>:"|?*'

    # Replace forbidden characters with underscores
    sanitized = filename
    for char in forbidden_chars:
        sanitized = sanitized.replace(char, '_')

    # Remove control characters (ASCII 0-31)
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32)

    # Handle reserved Windows names
    reserved_names = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }

    name_part = os.path.splitext(sanitized)[0].upper()
    if name_part in reserved_names:
        sanitized = f"_{sanitized}"

    # Remove trailing dots and spaces (Windows doesn't allow these)
    sanitized = sanitized.rstrip('. ')

    # Ensure filename is not empty
    if not sanitized:
        sanitized = "unnamed_file"

    return sanitized

def safe_copy_file(source_file: str, dest_file: str, max_retries: int = 3, retry_delay: int = 2) -> Tuple[bool, str]:
    """
    Safely copy a file with retry logic to handle file locks and special characters.

    Args:
        source_file: Path to the source file
        dest_file: Path to the destination file
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds

    Returns:
        Tuple of (success, error_message)
    """
    # Force garbage collection to release any file handles
    gc.collect()

    # Sanitize the destination file path to handle special characters
    dest_dir = os.path.dirname(dest_file)
    dest_filename = os.path.basename(dest_file)
    sanitized_filename = sanitize_filename(dest_filename)

    # If filename was changed, update the destination path
    if sanitized_filename != dest_filename:
        dest_file = os.path.join(dest_dir, sanitized_filename)

    for attempt in range(max_retries):
        try:
            # Check if file is locked by another process
            if os.path.exists(source_file):
                try:
                    # Try opening the file to see if it's locked
                    with open(source_file, 'rb') as f:
                        # Just read a byte to check access
                        f.read(1)
                except PermissionError:
                    if attempt < max_retries - 1:
                        print(f"  ⚠️ File appears to be locked, retrying in {retry_delay}s: {os.path.basename(source_file)}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        return False, "File is locked by another process"

            # Create destination directory if it doesn't exist
            # Handle special characters in directory path
            try:
                os.makedirs(dest_dir, exist_ok=True)
            except OSError as e:
                return False, f"Cannot create directory with special characters: {str(e)}"

            # Check if destination file exists and is locked
            if os.path.exists(dest_file):
                try:
                    # Try opening the destination file to see if it's locked
                    with open(dest_file, 'rb') as f:
                        f.read(1)
                except PermissionError:
                    if attempt < max_retries - 1:
                        print(f"  ⚠️ Destination file appears to be locked, retrying in {retry_delay}s: {os.path.basename(dest_file)}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        return False, "Destination file is locked by another process"

            # Perform the copy operation with proper encoding handling
            try:
                shutil.copy2(source_file, dest_file)
                return True, ""
            except UnicodeError as e:
                return False, f"Unicode/encoding error with special characters: {str(e)}"

        except PermissionError as e:
            if attempt < max_retries - 1:
                print(f"  ⚠️ Permission error, retrying in {retry_delay}s: {str(e)}")
                time.sleep(retry_delay)
            else:
                return False, f"Permission error after {max_retries} attempts: {str(e)}"

        except FileNotFoundError as e:
            return False, f"File not found: {str(e)}"

        except OSError as e:
            if "being used by another process" in str(e) and attempt < max_retries - 1:
                print(f"  ⚠️ File in use by another process, retrying in {retry_delay}s: {os.path.basename(source_file)}")
                time.sleep(retry_delay)
            else:
                return False, f"OS error (possibly special characters): {str(e)}"

        except Exception as e:
            return False, f"Unexpected error: {str(e)}"

    return False, f"Failed to copy file after {max_retries} attempts"

def check_file_locks_info():
    """
    Provide information about common processes that might lock files.
    """
    print("\n🔍 File Lock Troubleshooting Tips:")
    print("=" * 50)
    print("If you're experiencing file lock errors, try:")
    print("1. Close Excel, Word, or other Office applications")
    print("2. Close Windows Explorer windows browsing the folders")
    print("3. Disable real-time antivirus scanning temporarily")
    print("4. Run the script as Administrator")
    print("5. Restart Windows Explorer (taskkill /f /im explorer.exe && start explorer.exe)")
    print("6. Check if any backup software is running")
    print("=" * 50)

def normalize_path_for_source(true_path: str, source_dir: str) -> str:
    """
    Normalize the True Path to work with the source directory.

    This function handles cases where the True Path contains drive letters, absolute paths,
    and special characters like spaces and other Windows-allowed characters.

    Args:
        true_path: The True Path from CSV
        source_dir: The source directory provided by user (used for context)

    Returns:
        Normalized relative path that can be joined with source_dir
    """
    # Note: source_dir is used for context but the main logic focuses on true_path
    _ = source_dir  # Acknowledge the parameter to avoid warnings

    # Handle empty or None paths
    if not true_path:
        return ""

    # Normalize path separators to OS-specific separator
    # This handles mixed forward/backslashes in paths
    normalized_path = true_path.replace('/', os.sep).replace('\\', os.sep)

    # For your specific case, we need a simple solution
    # If the path contains a drive letter (like C:), extract just the part after the drive
    if len(normalized_path) > 2 and normalized_path[1] == ':':
        # Check if the path contains a drive letter like C:
        drive_letter = normalized_path[0].upper()

        # Extract the path after the drive letter and separator
        path_after_drive = normalized_path[3:] if len(normalized_path) > 3 else ""

        # For paths like C:\Users\<USER>\ part as a folder
        # This is the key fix for your specific scenario
        return f"{drive_letter}{os.sep}{path_after_drive}"

    # If it's already a relative path, use it as is
    if not os.path.isabs(normalized_path):
        return normalized_path

    # For other absolute paths without drive letters, just remove the leading separator
    return normalized_path.lstrip(os.sep + '/')

def read_paths_from_csv(csv_path: str, source_dir: str) -> List[Dict]:
    """
    Extract paths and additional information from the CSV file.

    Args:
        csv_path: Path to the CSV file
        source_dir: Source directory to normalize paths against

    Returns:
        List of dictionaries containing file information from the CSV
    """
    file_entries = []
    try:
        with open(csv_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            if "True Path" not in reader.fieldnames:
                print(f"Error: CSV file does not contain 'True Path' column. Available columns: {reader.fieldnames}")
                sys.exit(1)

            for row_index, row in enumerate(reader):
                true_path = row.get("True Path", "").strip()
                if true_path:
                    # Normalize the path to work with source directory
                    normalized_path = normalize_path_for_source(true_path, source_dir)

                    entry = {
                        "index": row_index + 1,
                        "path": normalized_path,  # Use normalized path
                        "original_true_path": true_path,  # Keep original for logging
                        "name": row.get("Name", os.path.basename(true_path)),
                        "tag": row.get("Tag", ""),
                        "file_ext": row.get("File Ext", os.path.splitext(true_path)[1].replace(".", "")),
                        "description": row.get("Description", ""),
                        "md5": row.get("MD5", ""),
                        "original_row": row
                    }
                    file_entries.append(entry)
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        sys.exit(1)

    return file_entries

def copy_files_with_structure(source_dir: str, output_dir: str, file_entries: List[Dict], verbose: bool = False) -> List[Dict]:
    """
    Copy files from source directory to output directory maintaining folder structure.

    Args:
        source_dir: Source directory containing the files
        output_dir: Output directory where files will be copied
        file_entries: List of file entry dictionaries from CSV
        verbose: Enable verbose output

    Returns:
        List of dictionaries with detailed information about each processed file
    """
    log_entries = []
    total_files = len(file_entries)

    print(f"\nProcessing {total_files} files...")

    for entry_index, entry in enumerate(file_entries):
        start_time = time.time()
        file_path = entry["path"]

        try:
            # Normalize path separators
            normalized_path = file_path.replace('\\', os.sep).replace('/', os.sep)

            # Create source and destination paths
            source_file = os.path.join(source_dir, normalized_path)

            # For destination, use the original filename from CSV "Name" field to test sanitization
            # This allows us to handle cases where the CSV Name has forbidden characters
            dest_dir_path = os.path.dirname(normalized_path)
            original_filename = entry["name"]  # This might contain forbidden characters
            dest_file = os.path.join(output_dir, dest_dir_path, original_filename)

            # Get source file information
            source_info = get_file_info(source_file)

            # Create comprehensive log entry
            log_entry = {
                "Index": entry["index"],
                "CSV_Row": entry_index + 1,
                "File_Name": entry["name"],
                "True_Path": entry.get("original_true_path", file_path),  # Use original True Path from CSV
                "Normalized_Path": file_path,  # Show the normalized path used for copying
                "Source_File": source_file,
                "Destination_File": dest_file,
                "File_Extension": entry["file_ext"],
                "Tag": entry["tag"],
                "Description": entry["description"],
                "Source_Exists": source_info["exists"],
                "Source_Size": source_info["size"],
                "Source_Size_Formatted": source_info["size_formatted"],
                "Source_Modified": source_info["modified_time"],
                "Source_Created": source_info["created_time"],
                "Source_MD5": source_info.get("md5_hash", ""),
                "CSV_MD5": entry["md5"],
                "MD5_Match": "",
                "Status": "",
                "Error": "",
                "Processing_Time": "",
                "Timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Check MD5 match if both are available
            if log_entry["Source_MD5"] and log_entry["CSV_MD5"]:
                log_entry["MD5_Match"] = "Yes" if log_entry["Source_MD5"].lower() == log_entry["CSV_MD5"].lower() else "No"

            # Progress indicator
            if verbose or (entry_index + 1) % 100 == 0:
                print(f"Processing {entry_index + 1}/{total_files}: {entry['name']}")

            # Check if source file exists
            if not source_info["exists"]:
                log_entry["Status"] = "Failed"
                log_entry["Error"] = "Source file not found"
                log_entries.append(log_entry)
                if verbose:
                    print(f"  ❌ Source file not found: {source_file}")
                continue

            # Check if filename needs sanitization
            dest_filename = os.path.basename(dest_file)
            sanitized_filename = sanitize_filename(dest_filename)
            filename_changed = sanitized_filename != dest_filename

            if filename_changed:
                dest_file = os.path.join(os.path.dirname(dest_file), sanitized_filename)
                log_entry["Destination_File"] = dest_file
                if verbose:
                    print(f"  🔧 Filename sanitized: '{dest_filename}' → '{sanitized_filename}'")

            # Use safe copy method with retry logic
            copy_success, copy_error = safe_copy_file(source_file, dest_file)

            if copy_success:
                # Get destination file info to verify copy
                dest_info = get_file_info(dest_file)

                # Update log entry with success information
                log_entry["Processing_Time"] = f"{time.time() - start_time:.2f}s"

                # Add filename sanitization info to log
                if filename_changed:
                    log_entry["Error"] = f"Filename sanitized: '{dest_filename}' → '{sanitized_filename}'"

                # Verify file was copied correctly
                if dest_info["exists"] and dest_info["size"] == source_info["size"]:
                    log_entry["Status"] = "Success"
                    if verbose:
                        print(f"  ✅ Copied: {entry['name']} ({source_info['size_formatted']})")
                else:
                    log_entry["Status"] = "Failed"
                    log_entry["Error"] = "Copy verification failed - size mismatch"
                    if verbose:
                        print(f"  ❌ Copy verification failed: {entry['name']}")
            else:
                log_entry["Status"] = "Failed"
                log_entry["Error"] = copy_error
                log_entry["Processing_Time"] = f"{time.time() - start_time:.2f}s"
                if verbose:
                    print(f"  ❌ Copy failed: {entry['name']} - {copy_error}")

            log_entries.append(log_entry)

        except Exception as e:
            log_entry = {
                "Index": entry.get("index", entry_index + 1),
                "CSV_Row": entry_index + 1,
                "File_Name": entry.get("name", "Unknown"),
                "True_Path": entry.get("original_true_path", file_path),  # Use original True Path from CSV
                "Normalized_Path": file_path,  # Show the normalized path used for copying
                "Source_File": source_file if 'source_file' in locals() else "",
                "Destination_File": dest_file if 'dest_file' in locals() else "",
                "File_Extension": entry.get("file_ext", ""),
                "Tag": entry.get("tag", ""),
                "Description": entry.get("description", ""),
                "Source_Exists": False,
                "Source_Size": 0,
                "Source_Size_Formatted": "0 B",
                "Source_Modified": "",
                "Source_Created": "",
                "Source_MD5": "",
                "CSV_MD5": entry.get("md5", ""),
                "MD5_Match": "",
                "Status": "Failed",
                "Error": str(e),
                "Processing_Time": f"{time.time() - start_time:.2f}s",
                "Timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            log_entries.append(log_entry)
            if verbose:
                print(f"  ❌ Error copying {entry.get('name', 'Unknown')}: {e}")

    return log_entries

def create_csv_log(log_entries: List[Dict], current_dir: str):
    """
    Create a comprehensive CSV log file with details of the copy operation.

    Args:
        log_entries: List of dictionaries with information about each processed file
        current_dir: Current working directory where the log will be saved
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(current_dir, f"File_Processing_Log_{timestamp}.csv")

    # Define columns for the log file in the desired order
    fieldnames = [
        "Index", "CSV_Row", "File_Name", "True_Path", "Normalized_Path", "Source_File", "Destination_File",
        "File_Extension", "Tag", "Description", "Source_Exists", "Source_Size",
        "Source_Size_Formatted", "Source_Modified", "Source_Created",
        "Source_MD5", "CSV_MD5", "MD5_Match", "Status", "Error",
        "Processing_Time", "Timestamp"
    ]

    try:
        with open(log_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(log_entries)

        print(f"\nDetailed log file created: {log_file}")
        return log_file
    except Exception as e:
        print(f"Error creating log file: {e}")
        return None

def generate_summary_report(log_entries: List[Dict], current_dir: str, output_dir: str, start_time: float, csv_path: str, source_dir: str):
    """
    Generate a summary report of the file processing operation.

    Args:
        log_entries: List of dictionaries with information about each processed file
        current_dir: Current working directory where the summary will be saved
        output_dir: Output directory where files were copied
        start_time: Start time of the operation
        csv_path: Path to the CSV file
        source_dir: Source directory
    """
    # Calculate statistics
    total_files = len(log_entries)
    success_count = sum(1 for entry in log_entries if entry["Status"] == "Success")
    failed_count = sum(1 for entry in log_entries if entry["Status"] == "Failed")
    success_rate = (success_count / total_files) * 100 if total_files > 0 else 0

    # Calculate total size
    total_size = sum(entry["Source_Size"] for entry in log_entries if entry["Status"] == "Success")
    total_size_formatted = format_file_size(total_size)

    # Calculate processing time
    total_time = time.time() - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    time_formatted = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"

    # Generate summary report
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = os.path.join(current_dir, f"Processing_Summary_{timestamp}.txt")

    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write(f"FILE PROCESSING SUMMARY\n")
            f.write("=" * 80 + "\n\n")

            f.write(f"Date and Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"CSV File: {csv_path}\n")
            f.write(f"Source Directory: {source_dir}\n")
            f.write(f"Output Directory: {output_dir}\n\n")

            f.write(f"Total Files Processed: {total_files}\n")
            f.write(f"Successfully Copied: {success_count} ({success_rate:.1f}%)\n")
            f.write(f"Failed: {failed_count} ({100-success_rate:.1f}%)\n")
            f.write(f"Total Data Copied: {total_size_formatted}\n")
            f.write(f"Total Processing Time: {time_formatted}\n\n")

            # Add error summary if there are failures
            if failed_count > 0:
                error_types = {}
                for entry in log_entries:
                    if entry["Status"] == "Failed":
                        error = entry["Error"]
                        error_types[error] = error_types.get(error, 0) + 1

                f.write("ERROR SUMMARY:\n")
                f.write("-" * 80 + "\n")
                for error, count in error_types.items():
                    f.write(f"- {error}: {count} files\n")

            f.write("\n" + "=" * 80 + "\n")

        print(f"\nSummary report created: {summary_file}")
        return summary_file
    except Exception as e:
        print(f"Error creating summary report: {e}")
        return None

def main():
    """Main function to execute the enhanced file copy script."""
    start_time = time.time()

    # Parse command line arguments
    args = parse_arguments()

    source_dir = args.source
    csv_path = args.csv
    output_dir = args.output if args.output else os.path.join(os.getcwd(), "Output")
    verbose = args.verbose
    troubleshoot = args.troubleshoot

    # Show troubleshooting info if requested
    if troubleshoot:
        check_file_locks_info()
        if not source_dir or not csv_path:
            print("\nTo run the file copy script, use:")
            print("python copy_files_from_csv.py -s <source_directory> -c <csv_file>")
            sys.exit(0)

    # Check required arguments
    if not source_dir or not csv_path:
        print("❌ Error: Both source directory (-s) and CSV file (-c) are required")
        sys.exit(1)

    # Display script header
    print("=" * 80)
    print("ENHANCED FILE COPY SCRIPT")
    print("=" * 80)
    print(f"Started at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Validate inputs
    if not os.path.exists(source_dir):
        print(f"❌ Error: Source directory not found: {source_dir}")
        sys.exit(1)

    if not os.path.exists(csv_path):
        print(f"❌ Error: CSV file not found: {csv_path}")
        sys.exit(1)

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    print(f"\n📁 Source directory: {source_dir}")
    print(f"📄 CSV file: {csv_path}")
    print(f"📂 Output directory: {output_dir}")
    print(f"🔍 Verbose mode: {'Enabled' if verbose else 'Disabled'}")

    # Read file entries from CSV
    print("\n📖 Reading file entries from CSV...")
    file_entries = read_paths_from_csv(csv_path, source_dir)
    print(f"✅ Found {len(file_entries)} file entries in CSV")

    # Show path normalization info in verbose mode
    if verbose and file_entries:
        print("\n🔧 Path normalization examples:")
        for entry in file_entries[:3]:  # Show first 3 examples
            print(f"  Original: {entry.get('original_true_path', 'N/A')}")
            print(f"  Normalized: {entry['path']}")
            print(f"  Will look for: {os.path.join(source_dir, entry['path'])}")
            print()

    # Copy files with enhanced logging
    print("\n🚀 Starting file copy operation...")
    log_entries = copy_files_with_structure(source_dir, output_dir, file_entries, verbose)

    # Create detailed log in current working directory
    current_dir = os.getcwd()
    print("\n📝 Creating detailed log file...")
    log_file = create_csv_log(log_entries, current_dir)

    # Generate summary report in current working directory
    print("📊 Generating summary report...")
    summary_file = generate_summary_report(log_entries, current_dir, output_dir, start_time, csv_path, source_dir)

    # Calculate and display final statistics
    success_count = sum(1 for entry in log_entries if entry["Status"] == "Success")
    failed_count = sum(1 for entry in log_entries if entry["Status"] == "Failed")
    success_rate = (success_count / len(file_entries)) * 100 if file_entries else 0

    # Calculate total size copied
    total_size = sum(entry["Source_Size"] for entry in log_entries if entry["Status"] == "Success")
    total_size_formatted = format_file_size(total_size)

    # Calculate processing time
    total_time = time.time() - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    time_formatted = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"

    # Display final summary
    print("\n" + "=" * 80)
    print("FINAL PROCESSING SUMMARY")
    print("=" * 80)
    print(f"📊 Total files processed: {len(file_entries)}")
    print(f"✅ Successfully copied: {success_count} ({success_rate:.1f}%)")
    print(f"❌ Failed: {failed_count} ({100-success_rate:.1f}%)")
    print(f"💾 Total data copied: {total_size_formatted}")
    print(f"⏱️  Total processing time: {time_formatted}")
    print(f"📄 Log file: {log_file}")
    print(f"📋 Summary report: {summary_file}")
    print("=" * 80)

    # Check for file lock errors and show troubleshooting info
    lock_errors = sum(1 for entry in log_entries if "locked" in entry.get("Error", "").lower() or "being used by another process" in entry.get("Error", "").lower())

    # Exit with appropriate code
    if failed_count > 0:
        print(f"\n⚠️  Warning: {failed_count} files failed to copy. Check the log file for details.")

        if lock_errors > 0:
            print(f"\n🔒 Detected {lock_errors} file lock errors.")
            check_file_locks_info()

        sys.exit(1)
    else:
        print(f"\n🎉 All files copied successfully!")
        sys.exit(0)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)